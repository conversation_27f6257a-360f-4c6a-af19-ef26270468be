# Frontend .dockerignore
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

dist/
dist-ssr/
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile
.dockerignore

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Build artifacts
build/
coverage/

# Logs
logs/
*.log
