#!/usr/bin/env python3
import os
import sys
import tempfile
import subprocess
import json
import logging

logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')

sys.path.append(os.path.join(os.path.dirname(__file__)))

from app.libs.analysis_engine import run_analysis

REPO_URL = "https://github.com/Daniel-<PERSON>k-developer/archon.git"

def clone_repository(repo_url: str, target_dir: str) -> bool:
    try:
        print(f"Cloning {repo_url} to {target_dir}")
        result = subprocess.run(
            ["git", "clone", repo_url, target_dir],
            capture_output=True,
            text=True,
            timeout=60
        )
        if result.returncode == 0:
            print("✅ Repository cloned successfully")
            return True
        else:
            print(f"❌ Git clone failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Git clone error: {e}")
        return False

def main():
    print("🧪 ARCHON CODE ANALYZER QUICK TEST")
    print("="*60)
    print(f"Repository: {REPO_URL}")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = os.path.join(temp_dir, "test_project")
        
        if not clone_repository(REPO_URL, project_path):
            print("❌ Failed to clone repository. Exiting.")
            return
        
        print(f"📁 Project cloned to: {project_path}")
        
        print("\n🚀 Running analysis...")
        try:
            report = run_analysis(project_path)
            
            print("✅ Analysis completed successfully")
            print(f"📊 Overall Score: {report['overall_score']:.1f}")
            print(f"📈 Quality Score: {report['quality_score']:.1f}")
            print(f"🛡️ Security Score: {report['security_score']:.1f}")
            print(f"🏗️ Structure Score: {report['structure_score']:.1f}")
            print(f"📦 Dependencies Score: {report['dependencies_score']:.1f}")
            print(f"🐛 Total Issues: {len(report['issues'])}")
            
            issues_by_category = {}
            issues_by_severity = {}
            issues_by_tool = {}
            
            for issue in report['issues']:
                category = issue['category']
                severity = issue['severity']
                tool = issue['tool']
                
                issues_by_category[category] = issues_by_category.get(category, 0) + 1
                issues_by_severity[severity] = issues_by_severity.get(severity, 0) + 1
                issues_by_tool[tool] = issues_by_tool.get(tool, 0) + 1
            
            print(f"\n📊 Issues by Category:")
            for category, count in issues_by_category.items():
                print(f"  {category}: {count} issues")
            
            print(f"\n⚠️ Issues by Severity:")
            for severity in ['Critical', 'High', 'Medium', 'Low']:
                if severity in issues_by_severity:
                    print(f"  {severity}: {issues_by_severity[severity]} issues")
            
            print(f"\n🔧 Issues by Tool:")
            for tool, count in issues_by_tool.items():
                print(f"  {tool}: {count} issues")
            
            with open("quick_analysis_report.json", 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n✅ Report saved to quick_analysis_report.json")
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
