# Backend .dockerignore
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Testing
.pytest_cache/
coverage.xml
.coverage

# Logs
*.log

# Docker
Dockerfile
.dockerignore

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Node modules (if any)
node_modules/
package*.json
yarn.lock
