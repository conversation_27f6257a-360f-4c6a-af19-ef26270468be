services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: archon-postgres
    environment:
      POSTGRES_DB: archon_dev
      POSTGRES_USER: archon_user
      POSTGRES_PASSWORD: archon_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - archon-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U archon_user -d archon_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache/Queue
  redis:
    image: redis:7-alpine
    container_name: archon-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - archon-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: archon-backend
    environment:
      - DATABASE_URL_DEV=******************************************************/archon_dev
      - DATABASE_URL_ADMIN_DEV=******************************************************/archon_dev
      - REDIS_URL=redis://redis:6379/0
      - ARCHON_SERVICE_TYPE=development
      - STACK_AUTH_PROJECT_ID=97bafa9b-894f-4d26-9dd4-6f0f69c41954
      - STACK_AUTH_PUBLISHABLE_CLIENT_KEY=pck_qz8nx0vvfaadked7dkqzg4whks8gzga6bb6txf10m9z4r
      - STACK_AUTH_JWKS_URL=https://api.stack-auth.com/api/v1/projects/97bafa9b-894f-4d26-9dd4-6f0f69c41954/.well-known/jwks.json
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    networks:
      - archon-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: archon-celery-worker
    environment:
      - DATABASE_URL_DEV=******************************************************/archon_dev
      - DATABASE_URL_ADMIN_DEV=******************************************************/archon_dev
      - REDIS_URL=redis://redis:6379/0
      - ARCHON_SERVICE_TYPE=development
    volumes:
      - ./backend:/app
    networks:
      - archon-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.libs.celery_worker worker --loglevel=info

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: archon-frontend
    environment:
      - VITE_API_URL=http://backend:8000
      - VITE_WS_API_URL=ws://backend:8000
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - archon-network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev -- --host 0.0.0.0

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  archon-network:
    driver: bridge
