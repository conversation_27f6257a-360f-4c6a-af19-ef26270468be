{"name": "archon", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@stackframe/react": "^2.8.25", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "input-otp": "^1.2.4", "lucide-react": "^0.447.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-resizable-panels": "^2.1.4", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react-window": "^1.8.8", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "vaul": "^1.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.16.10", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.2", "vite": "^5.4.8", "vite-plugin-html-inject": "^1.0.1", "vite-tsconfig-paths": "^5.0.1"}}